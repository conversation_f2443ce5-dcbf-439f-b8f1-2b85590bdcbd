import { describe, expect, it, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import * as React from "react";
import { z } from "zod";
import { Container, CInjection, VInjection, inject } from "../src/ioc";
import { provide } from "../src/provide";

describe("IoC Container", () => {
  describe("Container Component", () => {
    it("应该提供默认的空命名空间", () => {
      const TestComponent = () => {
        return (
          <Container>
            <div data-testid="content">Test Content</div>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("content")).toBeInTheDocument();
    });

    it("应该支持自定义命名空间", () => {
      const TestComponent = () => {
        return (
          <Container namespace="test-namespace">
            <div data-testid="content">Test Content</div>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("content")).toBeInTheDocument();
    });

    it("应该支持嵌套容器", () => {
      const TestComponent = () => {
        return (
          <Container namespace="outer">
            <Container namespace="inner">
              <div data-testid="nested-content">Nested Content</div>
            </Container>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("nested-content")).toBeInTheDocument();
    });
  });

  describe("VInjection - 值注入", () => {
    it("应该能够注入基本类型值", () => {
      const numberSchema = z.number();
      const stringSchema = z.string();

      const TestComponent = () => {
        return (
          <Container>
            <VInjection schema={numberSchema} val={42} />
            <VInjection schema={stringSchema} val="hello" />
            <div data-testid="injected">Values injected</div>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("injected")).toBeInTheDocument();
    });

    it("应该能够注入对象值", () => {
      const objectSchema = z.object({
        name: z.string(),
        age: z.number(),
      });

      const testValue = { name: "John", age: 30 };

      const TestComponent = () => {
        return (
          <Container>
            <VInjection schema={objectSchema} val={testValue} />
            <div data-testid="object-injected">Object injected</div>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("object-injected")).toBeInTheDocument();
    });

    it("应该支持在不同命名空间中注入相同schema的不同值", () => {
      const numberSchema = z.number();

      const TestComponent = () => {
        return (
          <div>
            <Container namespace="ns1">
              <VInjection schema={numberSchema} val={100} />
            </Container>
            <Container namespace="ns2">
              <VInjection schema={numberSchema} val={200} />
            </Container>
            <div data-testid="multi-namespace">Multi namespace injection</div>
          </div>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("multi-namespace")).toBeInTheDocument();
    });
  });

  describe("CInjection - 构造函数注入", () => {
    it("应该能够注入构造函数", () => {
      const userSchema = z.object({
        name: z.string(),
        email: z.string(),
      });

      class User {
        constructor(
          public name: string,
          public email: string
        ) {}
      }

      const TestComponent = () => {
        return (
          <Container>
            <CInjection schema={userSchema} ctor={User} />
            <div data-testid="ctor-injected">Constructor injected</div>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("ctor-injected")).toBeInTheDocument();
    });

    it("应该支持在不同命名空间中注入不同的构造函数", () => {
      const serviceSchema = z.object({
        process: z.function(),
      });

      class ServiceA {
        process() {
          return "A";
        }
      }

      class ServiceB {
        process() {
          return "B";
        }
      }

      const TestComponent = () => {
        return (
          <div>
            <Container namespace="serviceA">
              <CInjection schema={serviceSchema} ctor={ServiceA} />
            </Container>
            <Container namespace="serviceB">
              <CInjection schema={serviceSchema} ctor={ServiceB} />
            </Container>
            <div data-testid="multi-ctor">Multiple constructors</div>
          </div>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("multi-ctor")).toBeInTheDocument();
    });
  });

  describe("inject装饰器", () => {
    it("应该返回装饰器函数", () => {
      const configSchema = z.object({
        apiUrl: z.string(),
        timeout: z.number(),
      });

      const decorator = inject(configSchema);
      expect(typeof decorator).toBe("function");
    });

    it("应该在指定命名空间中查找依赖", () => {
      const messageSchema = z.string();
      const decorator = inject(messageSchema, "messages");
      expect(typeof decorator).toBe("function");
    });

    it("应该抛出错误当装饰非属性时", () => {
      const schema = z.string();
      const decorator = inject(schema);

      expect(() => {
        decorator(undefined, {
          static: true,
          kind: "field",
        } as ClassFieldDecoratorContext);
      }).toThrow("不能装饰静态属性");

      expect(() => {
        decorator(undefined, {
          static: false,
          kind: "method",
        } as any);
      }).toThrow("不能装饰非属性");
    });

    it("应该返回初始化函数", () => {
      const schema = z.string();
      const decorator = inject(schema);

      const initFunction = decorator(undefined, {
        static: false,
        kind: "field",
      } as ClassFieldDecoratorContext);

      expect(typeof initFunction).toBe("function");
    });
  });

  describe("集成测试", () => {
    it("应该支持复杂的依赖注入场景", () => {
      // 定义schemas
      const databaseConfigSchema = z.object({
        host: z.string(),
        port: z.number(),
      });

      const loggerSchema = z.object({
        log: z.function(),
      });

      const userServiceSchema = z.object({
        getUser: z.function(),
      });

      // 定义类
      class Logger {
        log(message: string) {
          return `[LOG] ${message}`;
        }
      }

      class DatabaseService {
        config: z.infer<typeof databaseConfigSchema> = {
          host: "localhost",
          port: 3306,
        };

        logger: Logger | undefined;

        connect() {
          return `Connected to ${this.config.host}:${this.config.port}`;
        }
      }

      class UserService {
        logger: Logger | undefined;

        getUser(id: number) {
          return { id, name: `User ${id}` };
        }
      }

      const TestComponent = () => {
        const [dbService] = React.useState(() => new DatabaseService());
        const [userService] = React.useState(() => new UserService());

        return (
          <Container namespace="app">
            <VInjection
              schema={databaseConfigSchema}
              val={{ host: "prod-db.example.com", port: 5432 }}
            />
            <CInjection schema={loggerSchema} ctor={Logger} />
            <CInjection schema={userServiceSchema} ctor={UserService} />

            <div data-testid="db-connection">{dbService.connect()}</div>
            <div data-testid="user-data">
              {JSON.stringify(userService.getUser(1))}
            </div>
          </Container>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("db-connection")).toBeInTheDocument();
      expect(screen.getByTestId("user-data")).toBeInTheDocument();
    });

    it("应该支持多个容器和命名空间", () => {
      const configSchema = z.object({
        value: z.string(),
      });

      const TestComponent = () => {
        return (
          <div>
            <Container namespace="env1">
              <VInjection schema={configSchema} val={{ value: "production" }} />
              <div data-testid="env1-container">Environment 1</div>
            </Container>

            <Container namespace="env2">
              <VInjection
                schema={configSchema}
                val={{ value: "development" }}
              />
              <div data-testid="env2-container">Environment 2</div>
            </Container>

            <Container>
              <VInjection schema={configSchema} val={{ value: "default" }} />
              <div data-testid="default-container">Default Environment</div>
            </Container>
          </div>
        );
      };

      render(<TestComponent />);
      expect(screen.getByTestId("env1-container")).toBeInTheDocument();
      expect(screen.getByTestId("env2-container")).toBeInTheDocument();
      expect(screen.getByTestId("default-container")).toBeInTheDocument();
    });
  });
});
